/* responsive-fixes.css - Correcciones específicas para responsive */

/* Ocultar el botón hamburguesa en desktop */
.menu-toggle {
    display: none;
}

/* Overlay para el menú móvil */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* Correcciones para inicio.css */
@media (max-width: 768px) {
    /* Asegurar que el título esté visible */
    .contenido-principal {
        padding-top: 120px !important;
        margin-top: 0 !important;
        position: relative !important;
        z-index: 10 !important;
    }

    .contenido-principal h1 {
        z-index: 1000 !important;
        position: relative !important;
        background-color: rgba(255, 255, 255, 0.98) !important;
        padding: 20px !important;
        border-radius: 12px !important;
        margin-bottom: 25px !important;
        margin-top: 30px !important;
        box-shadow: 0 6px 12px rgba(0,0,0,0.15) !important;
        font-size: 1.8rem !important;
        text-align: center !important;
        color: #2c3e50 !important;
        font-weight: bold !important;
    }

    .contenido-principal h3 {
        z-index: 1000 !important;
        position: relative !important;
        background-color: rgba(255, 255, 255, 0.95) !important;
        padding: 15px !important;
        border-radius: 10px !important;
        margin-bottom: 30px !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        text-align: center !important;
        color: #34495e !important;
    }

    /* Alinear logo con buscador */
    .menu-superior {
        align-items: center !important;
        padding: 10px 15px !important;
    }

    .menu-superior .logo {
        display: flex !important;
        align-items: center !important;
    }

    /* Mejorar el buscador en móviles */
    .menu-superior .buscador input {
        background-color: rgba(255, 255, 255, 0.9) !important;
        color: #333 !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .menu-superior .buscador input::placeholder {
        color: #666 !important;
    }

    .menu-superior .buscador button {
        color: #333 !important;
    }

    /* Ajustar la imagen QR */
    .cuadro-qr {
        margin: 20px auto;
        padding: 20px;
    }

    .cuadro-qr img {
        width: 150px;
        height: 150px;
    }
}

/* Correcciones para principal.css - SOLO RESPONSIVE */
@media (max-width: 768px) {
    /* Reorganizar header en móviles */
    .header-principal {
        flex-direction: column !important;
        padding: 8px 15px !important;
        min-height: auto !important;
        align-items: center !important;
    }

    .header-container {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        gap: 8px !important;
        align-items: center !important;
    }

    /* Primera fila: Logo y buscador */
    .header-principal .header-container {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 8px !important;
        gap: 10px !important;
        width: 100% !important;
    }

    .header-principal .logo {
        order: 1 !important;
        flex: 0 0 auto !important;
    }

    .header-principal .logo img {
        height: 30px !important;
        width: 30px !important;
    }

    .header-principal .buscador {
        order: 2 !important;
        flex: 1 !important;
        max-width: 200px !important;
        margin: 0 !important;
    }

    .header-principal .buscador input {
        width: 100% !important;
        padding: 6px 10px !important;
        font-size: 12px !important;
    }

    /* Ocultar usuario original */
    .header-principal .usuario {
        display: none !important;
    }

    /* Ocultar menú original */
    .header-principal .menu-principal {
        display: none !important;
    }

    /* Crear nueva estructura para menú desplegable y perfil */
    .mobile-menu-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        width: 100% !important;
        margin-top: 5px !important;
    }

    /* Árboles destacados en 2x2 */
    .grid-arboles {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 12px !important;
        padding: 12px !important;
        margin: 0 auto !important;
        max-width: 100% !important;
    }

    .arbol-card .info-arbol .detalles p {
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-size: 0.75rem !important;
        line-height: 1.3 !important;
    }

    .imagen-arbol {
        height: 120px !important;
    }

    .arbol-card .info-arbol h3 {
        font-size: 0.8rem !important;
        line-height: 1.2 !important;
    }

    .arbol-card .info-arbol h3 span {
        font-size: 0.7rem !important;
        display: block !important;
        margin-top: 2px !important;
    }

    /* Corregir botón "Explorar" */
    .arbol-card .btn {
        padding: 4px 8px !important;
        font-size: 10px !important;
        border-radius: 15px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
    }

    /* Estilos para menú desplegable móvil */
    .mobile-dropdown {
        position: relative;
        display: inline-block;
    }

    .mobile-dropdown-toggle {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 12px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .mobile-dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: #1e5631;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        border-radius: 5px;
        z-index: 1001;
        margin-top: 5px;
    }

    .mobile-dropdown-content.show {
        display: block;
    }

    .mobile-dropdown-content a {
        color: white;
        padding: 10px 15px;
        text-decoration: none;
        display: block;
        font-size: 12px;
        transition: background-color 0.3s;
    }

    .mobile-dropdown-content a:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Perfil móvil */
    .mobile-user {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .mobile-user img {
        height: 28px;
        width: 28px;
        border-radius: 50%;
        border: 1px solid white;
    }
}

@media (max-width: 480px) {
    .header-principal {
        flex-direction: column !important;
        padding: 8px !important;
        align-items: center !important;
    }

    .header-principal .logo,
    .header-principal .buscador,
    .header-principal .menu-principal,
    .header-principal .usuario {
        width: 100% !important;
        margin: 3px 0 !important;
        justify-content: center !important;
    }

    .header-principal .buscador {
        max-width: 90% !important;
    }

    .grid-arboles {
        grid-template-columns: 1fr !important;
    }
}

/* Correcciones para gestión */
@media (max-width: 768px) {
    .menu-toggle {
        display: block !important;
        background: none;
        border: none;
        color: var(--menu-text, white);
        font-size: 20px;
        cursor: pointer;
        padding: 5px;
        order: 1;
    }

    .header-principal .logo-container {
        order: 3 !important;
    }

    .header-principal .user-section {
        order: 2 !important;
        margin-left: 10px !important;
    }
}

/* Badges para estados */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 12px;
    text-align: center;
    white-space: nowrap;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* Botones de acciones */
.acciones-cell {
    white-space: nowrap;
}

.acciones-botones {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .acciones-botones {
        flex-direction: column;
        gap: 3px;
    }

    .acciones-botones .btn {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }
}

/* Correcciones adicionales para texto truncado */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .text-truncate {
        max-width: 100px;
    }
}

@media (max-width: 480px) {
    .text-truncate {
        max-width: 80px;
    }
}

/* Estilos para menú desplegable del footer */
.footer-dropdown {
    position: relative;
    display: inline-block;
}

.footer-dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.footer-dropdown-content {
    display: none;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #2c3e50;
    min-width: 200px;
    box-shadow: 0 -8px 16px rgba(0,0,0,0.2);
    border-radius: 5px;
    z-index: 1001;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-dropdown-content.show {
    display: block;
}

.footer-dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.3s;
    font-size: 13px;
}

.footer-dropdown-content a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.footer-dropdown-content a:first-child {
    border-radius: 5px 5px 0 0;
}

.footer-dropdown-content a:last-child {
    border-radius: 0 0 5px 5px;
}

@media (max-width: 768px) {
    .footer-dropdown-content {
        min-width: 180px;
        font-size: 12px;
    }

    .footer-dropdown-toggle {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* Correcciones específicas para gestión en móviles */
@media (max-width: 768px) {
    /* Cambiar a menú horizontal en móviles */
    .gestion-body .header-principal {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 60px !important;
        background: var(--menu-bg) !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 0 15px !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
        z-index: 1000 !important;
        flex-direction: row !important;
    }

    /* Mantener fondo original en móviles */
    .gestion-body .container {
        margin-left: 0 !important;
        margin-top: 60px !important;
        padding: 15px 10px !important;
        width: 100% !important;
        min-height: calc(100vh - 60px) !important;
        box-sizing: border-box !important;
    }

    /* Ocultar menú lateral original en móviles */
    .gestion-body .menu-principal {
        display: none !important;
    }

    /* Crear estructura horizontal para gestión móvil */
    .gestion-body .mobile-gestion-menu {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        width: 100% !important;
    }

    /* Menú desplegable para gestión móvil */
    .gestion-body .gestion-dropdown {
        position: relative !important;
        display: inline-block !important;
    }

    .gestion-body .gestion-dropdown-toggle {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: var(--menu-text) !important;
        font-size: 14px !important;
        cursor: pointer !important;
        padding: 8px 12px !important;
        border-radius: 5px !important;
        transition: background-color 0.3s !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .gestion-body .gestion-dropdown-toggle:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }

    .gestion-body .gestion-dropdown-content {
        display: none !important;
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        background-color: var(--menu-bg) !important;
        min-width: 250px !important;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
        border-radius: 5px !important;
        z-index: 1001 !important;
        margin-top: 5px !important;
        max-height: 400px !important;
        overflow-y: auto !important;
    }

    .gestion-body .gestion-dropdown-content.show {
        display: block !important;
    }

    .gestion-body .gestion-dropdown-content a {
        color: var(--menu-text) !important;
        padding: 12px 16px !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        transition: background-color 0.3s !important;
    }

    .gestion-body .gestion-dropdown-content a:hover {
        background-color: var(--menu-hover) !important;
    }

    .gestion-body .gestion-dropdown-content a i {
        margin-right: 10px !important;
        width: 20px !important;
    }

    /* Perfil y logo para gestión móvil */
    .gestion-body .mobile-right-section {
        display: flex !important;
        align-items: center !important;
        gap: 15px !important;
    }

    .gestion-body .mobile-user-section {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        cursor: pointer !important;
    }

    .gestion-body .mobile-user-section img {
        width: 30px !important;
        height: 30px !important;
        border-radius: 50% !important;
        border: 1px solid white !important;
    }

    .gestion-body .mobile-logo {
        width: 35px !important;
        height: 35px !important;
        border-radius: 50% !important;
    }

    /* Ocultar elementos originales en móviles */
    .gestion-body .logo-container {
        display: none !important;
    }

    .gestion-body .user-section {
        display: none !important;
    }
}
