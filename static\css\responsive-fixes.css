/* responsive-fixes.css - Correcciones específicas para responsive */

/* Ocultar el botón hamburguesa en desktop */
.menu-toggle {
    display: none;
}

/* Overlay para el menú móvil */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* Correcciones para inicio.css */
@media (max-width: 768px) {
    /* Asegurar que el título esté visible */
    .contenido-principal h1 {
        z-index: 100 !important;
        position: relative !important;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .contenido-principal h3 {
        z-index: 100 !important;
        position: relative !important;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 8px;
        border-radius: 6px;
        margin-bottom: 25px;
    }

    /* Mejorar el buscador en móviles */
    .menu-superior .buscador input {
        background-color: rgba(255, 255, 255, 0.9) !important;
        color: #333 !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .menu-superior .buscador input::placeholder {
        color: #666 !important;
    }

    .menu-superior .buscador button {
        color: #333 !important;
    }

    /* Ajustar la imagen QR */
    .cuadro-qr {
        margin: 20px auto;
        padding: 20px;
    }

    .cuadro-qr img {
        width: 150px;
        height: 150px;
    }
}

/* Correcciones para principal.css */
@media (max-width: 768px) {
    /* Reducir el menú en principal */
    .header-principal {
        padding: 5px 10px !important;
        min-height: 50px !important;
    }

    .header-principal .logo img {
        height: 35px !important;
        width: 35px !important;
    }

    .header-principal .buscador {
        max-width: 200px !important;
        margin: 3px 8px !important;
    }

    .header-principal .menu-principal ul {
        gap: 5px !important;
        margin: 3px 0 !important;
    }

    .header-principal .menu-principal ul li a {
        padding: 3px 6px !important;
        font-size: 11px !important;
    }

    .header-principal .usuario img {
        height: 30px !important;
        width: 30px !important;
    }

    .header-principal .usuario span {
        font-size: 11px !important;
    }

    /* Árboles destacados en 2x2 */
    .grid-arboles {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }

    .arbol-card .info-arbol .detalles p {
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-size: 0.75rem !important;
    }

    .imagen-arbol {
        height: 120px !important;
    }

    .arbol-card .info-arbol h3 {
        font-size: 0.85rem !important;
    }

    .arbol-card .info-arbol h3 span {
        font-size: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    .header-principal {
        flex-direction: column !important;
        padding: 8px !important;
        align-items: center !important;
    }

    .header-principal .logo,
    .header-principal .buscador,
    .header-principal .menu-principal,
    .header-principal .usuario {
        width: 100% !important;
        margin: 3px 0 !important;
        justify-content: center !important;
    }

    .header-principal .buscador {
        max-width: 90% !important;
    }

    .grid-arboles {
        grid-template-columns: 1fr !important;
    }
}

/* Correcciones para gestión */
@media (max-width: 768px) {
    .menu-toggle {
        display: block !important;
        background: none;
        border: none;
        color: var(--menu-text, white);
        font-size: 20px;
        cursor: pointer;
        padding: 5px;
        order: 1;
    }

    .header-principal .logo-container {
        order: 3 !important;
    }

    .header-principal .user-section {
        order: 2 !important;
        margin-left: 10px !important;
    }
}

/* Badges para estados */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 12px;
    text-align: center;
    white-space: nowrap;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* Botones de acciones */
.acciones-cell {
    white-space: nowrap;
}

.acciones-botones {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .acciones-botones {
        flex-direction: column;
        gap: 3px;
    }

    .acciones-botones .btn {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }
}

/* Correcciones adicionales para texto truncado */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .text-truncate {
        max-width: 100px;
    }
}

@media (max-width: 480px) {
    .text-truncate {
        max-width: 80px;
    }
}
