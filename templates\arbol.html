<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Árbol - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/gestion.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common_header.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/menu_buttons.css') }}">
    <!-- Correcciones responsive adicionales -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-fixes.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <header class="header-principal">
        <!-- Menú desplegable -->
        <div class="dropdown-menu">
            <button type="button" class="dropdown-toggle" onclick="toggleDropdown()">
                <i class="fas fa-bars"></i> Menú
            </button>
            <div class="dropdown-content" id="dropdownContent">
                <a href="{{ url_for('gestion') }}"><i class="fas fa-home"></i>Inicio</a>
                <a href="{{ url_for('arbol') }}"><i class="fas fa-tree"></i>Árboles</a>
                <a href="{{ url_for('centro') }}"><i class="fas fa-university"></i>Centros</a>
                <a href="{{ url_for('especie') }}"><i class="fas fa-leaf"></i>Especies</a>
                <a href="{{ url_for('uso_arbol') }}"><i class="fas fa-tags"></i>Usos</a>
                <a href="{{ url_for('tipo_bosque') }}"><i class="fas fa-mountain"></i>Tipos de Bosque</a>
                <a href="{{ url_for('curiosidades') }}"><i class="fas fa-lightbulb"></i>Curiosidades</a>
                <a href="{{ url_for('interacciones') }}"><i class="fas fa-network-wired"></i>Interacciones Ecológicas</a>
                <a href="{{ url_for('qr') }}"><i class="fas fa-qrcode"></i>QR</a>
                <a href="{{ url_for('sugerencias') }}"><i class="fas fa-comments"></i>Sugerencias</a>
                <a href="{{ url_for('gestion_usuarios') }}"><i class="fas fa-users"></i>Usuarios</a>
            </div>
        </div>

        <!-- Perfil de usuario -->
        <div class="user-section">
            <a href="{{ url_for('perfil') }}" class="user-profile-link" style="display: flex; align-items: center; text-decoration: none;">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
        </div>

        <!-- Logo en esquina superior derecha -->
        <div class="logo-container">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo">
        </div>
    </header>

    <main class="container">
        <!-- Formulario para registrar árboles -->
        <div class="contenedor-seccion">
            <h2>Árbol</h2>
            <form method="POST" action="{{ url_for('arbol') }}" class="formulario-gestion" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="especie">Especie:</label>
                    <select id="especie" name="especie" class="form-control" required>
                        {% for esp in especies %}
                        <option value="{{ esp.IDEspecie }}">{{ esp.NombreCientifico }} ({{ esp.NombreVulgar }})</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="caracteristicas">Características:</label>
                    <textarea id="caracteristicas" name="caracteristicas" class="form-control" placeholder="Describa las características físicas del árbol"></textarea>
                </div>

                <div class="form-group">
                    <label for="servicios_ecosistemicos">Servicios Ecosistémicos:</label>
                    <textarea id="servicios_ecosistemicos" name="servicios_ecosistemicos" class="form-control" placeholder="Describa los servicios ecosistémicos que proporciona este árbol"></textarea>
                </div>

                <div class="form-group">
                    <label for="tipo_bosque">Tipo de Bosque:</label>
                    <select id="tipo_bosque" name="tipo_bosque" class="form-control" required>
                        {% for bosque in tipos_bosque %}
                        <option value="{{ bosque.IDTipoBosque }}">{{ bosque.Nombre }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="centro">Centro:</label>
                    <select id="centro" name="centro" class="form-control" required>
                        {% for centro in centros %}
                        <option value="{{ centro.IDCentro }}">{{ centro.NombreCentro }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="imagen">Imagen del Árbol:</label>
                    <input type="file" id="imagen" name="imagen" class="form-control" accept="image/*">
                    <small class="form-text text-muted">Selecciona una imagen del árbol (opcional). Formatos permitidos: JPG, PNG, GIF.</small>
                </div>

                <div class="form-group">
                    <label for="descripcion">Descripción:</label>
                    <textarea id="descripcion" name="descripcion" class="form-control" placeholder="Breve descripción del árbol"></textarea>
                </div>

                <div class="form-group">
                    <label for="estado">Estado:</label>
                    <select id="estado" name="estado" class="form-control" required>
                        {% for estado in estados %}
                        <option value="{{ estado.IDEstado }}">{{ estado.NombreEstado }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Guardar
                </button>
            </form>
        </div>

        <!-- Lista de árboles registrados -->
        <div class="contenedor-tabla">
            <h2>Árboles Registrados</h2>
            <div class="tabla-contenedor">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Especie</th>
                            <th>Nombre Vulgar</th>
                            <th>Descripción</th>
                            <th>Características</th>
                            <th>Servicios Ecosistémicos</th>
                            <th>Tipo de Bosque</th>
                            <th>Centro</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for arbol in arboles %}
                        <tr>
                            <td>{{ arbol.IDArbol }}</td>
                            <td>{{ arbol.EspecieNombreCientifico }}</td>
                            <td>{{ arbol.EspecieNombreVulgar }}</td>
                            <td>{{ arbol.Descripcion|default('No disponible')|truncate(50) }}</td>
                            <td>{{ arbol.Caracteristicas|default('No disponible')|truncate(50) }}</td>
                            <td>{{ arbol.ServiciosEcosistemicos|default('No disponible')|truncate(50) }}</td>
                            <td>{{ arbol.TipoBosqueNombre }}</td>
                            <td>{{ arbol.CentroNombre }}</td>
                            <td>
                                <span class="badge {% if arbol.Estado == 1 %}badge-success{% else %}badge-danger{% endif %}">
                                    {{ arbol.EstadoNombre }}
                                </span>
                            </td>
                            <td class="acciones-cell">
                                <div class="acciones-botones">
                                    <a href="{{ url_for('editar_arbol', id=arbol.IDArbol) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="{{ url_for('eliminar_arbol', id=arbol.IDArbol) }}" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar este árbol?');">
                                        <i class="fas fa-trash"></i> Eliminar
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        // Función para el menú desplegable
        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            dropdown.classList.toggle('show');
        }

        // Cerrar el dropdown si se hace clic fuera de él
        window.onclick = function(event) {
            if (!event.target.matches('.dropdown-toggle') && !event.target.matches('.dropdown-toggle i')) {
                const dropdowns = document.getElementsByClassName('dropdown-content');
                for (let i = 0; i < dropdowns.length; i++) {
                    const openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }
    </script>
</body>
</html>